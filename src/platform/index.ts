/**
 * Platform abstraction layer for roo-code
 * 
 * This module provides a platform-agnostic interface that allows roo-code
 * to work across different environments (VS Code extension, standalone app, web app, etc.)
 */

// Export all interfaces
export * from './interfaces'

// Export VS Code implementations
export * from './vscode'

// Export platform factory
export { PlatformFactory, createPlatformServices, detectPlatformType } from './PlatformFactory'

// Re-export commonly used types for convenience
export type {
  IPlatformServices,
  IFileSystem,
  IWebview,
  IWebviewProvider,
  IConfiguration,
  ILogger,
  IWorkspace,
  PlatformType,
  PlatformInitOptions
} from './interfaces'
