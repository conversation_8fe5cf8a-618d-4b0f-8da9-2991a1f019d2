import * as vscode from "vscode"
import { 
  IPlatformFactory, 
  IPlatformServices, 
  PlatformType, 
  PlatformInitOptions 
} from "./interfaces"
import { VSCodePlatformServices } from "./vscode"

/**
 * Factory for creating platform services based on the current environment
 */
export class PlatformFactory implements IPlatformFactory {
  private static instance?: PlatformFactory

  /**
   * Get the singleton instance of the platform factory
   */
  static getInstance(): PlatformFactory {
    if (!this.instance) {
      this.instance = new PlatformFactory()
    }
    return this.instance
  }

  /**
   * Create platform services for the specified type
   */
  async createPlatformServices(
    type: PlatformType, 
    options?: PlatformInitOptions
  ): Promise<IPlatformServices> {
    switch (type) {
      case PlatformType.VSCode:
        return this.createVSCodePlatformServices(options)
      
      case PlatformType.Standalone:
        throw new Error("Standalone platform services not yet implemented")
      
      case PlatformType.Web:
        throw new Error("Web platform services not yet implemented")
      
      case PlatformType.Server:
        throw new Error("Server platform services not yet implemented")
      
      default:
        throw new Error(`Unsupported platform type: ${type}`)
    }
  }

  /**
   * Detect the current platform type based on the environment
   */
  detectPlatformType(): PlatformType {
    // Check if we're running in VS Code extension context
    if (typeof vscode !== 'undefined' && vscode.extensions) {
      return PlatformType.VSCode
    }

    // Check if we're running in a browser
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      return PlatformType.Web
    }

    // Check if we're running in Node.js server context
    if (typeof process !== 'undefined' && process.versions?.node) {
      // If we have access to file system but no VS Code APIs, assume standalone
      return PlatformType.Standalone
    }

    // Default fallback
    return PlatformType.Standalone
  }

  /**
   * Check if a platform type is supported
   */
  isPlatformSupported(type: PlatformType): boolean {
    switch (type) {
      case PlatformType.VSCode:
        return true
      case PlatformType.Standalone:
      case PlatformType.Web:
      case PlatformType.Server:
        return false // Not yet implemented
      default:
        return false
    }
  }

  /**
   * Create VS Code platform services
   */
  private async createVSCodePlatformServices(options?: PlatformInitOptions): Promise<VSCodePlatformServices> {
    // In VS Code context, we need the extension context
    // This should be provided through options or we need to get it from the current extension
    const context = (options as any)?.context
    if (!context) {
      throw new Error("VS Code extension context is required for VS Code platform services")
    }

    const services = new VSCodePlatformServices(context)
    await services.initialize(options)
    return services
  }

  /**
   * Create platform services for the current environment
   * Automatically detects the platform type and creates appropriate services
   */
  async createForCurrentEnvironment(
    context?: vscode.ExtensionContext,
    options?: PlatformInitOptions
  ): Promise<IPlatformServices> {
    const platformType = this.detectPlatformType()
    
    if (platformType === PlatformType.VSCode && context) {
      return this.createPlatformServices(platformType, { ...options, context })
    }
    
    return this.createPlatformServices(platformType, options)
  }
}

/**
 * Convenience function to create platform services for the current environment
 */
export async function createPlatformServices(
  context?: vscode.ExtensionContext,
  options?: PlatformInitOptions
): Promise<IPlatformServices> {
  const factory = PlatformFactory.getInstance()
  return factory.createForCurrentEnvironment(context, options)
}

/**
 * Convenience function to detect the current platform type
 */
export function detectPlatformType(): PlatformType {
  const factory = PlatformFactory.getInstance()
  return factory.detectPlatformType()
}
